import React from 'react';
import { StyleSheet, TouchableOpacity, Text, Platform } from 'react-native';
import { useRecoilValue } from 'recoil';
import Colors from '../../Constants/Colors';
import { sessionState } from '../../store/session/atoms';
import { useIsAdFree } from '../../contexts/IAPContext';
import { reconnectWithAdLogic } from '../../utils/reconnectUtils';

export default function ReconnectButton() {
  const session = useRecoilValue(sessionState);
  const isAdFree = useIsAdFree(); // Use optimized cached ad-free check

  const reconnect = () => {
    reconnectWithAdLogic(session.partners, isAdFree);
  };
  return (
    <TouchableOpacity style={styles.btn} onPress={reconnect}>
      <Text style={styles.txt}>Find new friend</Text>
    </TouchableOpacity>
  );
}
const styles = StyleSheet.create({
  btn: {
    backgroundColor: Colors.actionBtnColor,
    alignSelf: 'center',
    height: 40,
    justifyContent: 'center',
    paddingHorizontal: 10,
    marginVertical: 10,
    borderRadius: 12,
  },
  txt: {
    color: 'white',
    textAlignVertical: 'center',
    fontFamily:
      Platform.OS === 'android' ? 'PoppinsRegular-B2Bw' : 'Poppins-Regular',
  },
});
