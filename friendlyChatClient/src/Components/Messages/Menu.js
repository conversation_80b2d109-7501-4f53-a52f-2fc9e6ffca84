import React, { useEffect, useState, useRef } from 'react';
import {
  Keyboard,
  Platform,
  StyleSheet,
  TouchableOpacity,
  Text,
  ScrollView,
} from 'react-native';
import {
  Menu,
  MenuOption,
  MenuOptions,
  MenuTrigger,
} from 'react-native-popup-menu';
import { useTheme } from 'react-native-paper';
import Tooltip from 'react-native-walkthrough-tooltip';
import { useRecoilState } from 'recoil';
import { TooltipState } from '../../store/tooltip/atoms';
import { Stage } from '../../store/tooltip/enum';
import Colors from '../../Constants/Colors';

export default function ActionInputMenu(props) {
  const theme = useTheme();
  const [toolTipStage, setToolTipStage] = useRecoilState(TooltipState);
  const [visible, setVisible] = useState(false);
  const menuRef = useRef(null);

  const hideMenu = () => setVisible(false);
  const openMenu = () => {
    Keyboard.dismiss();
    if (menuRef.current) {
      menuRef.current.open();
    }
    setVisible(true);
  };
  useEffect(() => {
    if (toolTipStage === Stage.Avatar) {
      menuRef.current.close();
    }
  }, [toolTipStage]);

  const menuAction = action => {
    hideMenu();
    action();
  };

  const styles = StyleSheet.create({
    font: {
      color: theme.colors.text,
      fontSize: 16,
      fontFamily:
        Platform.OS === 'ios' ? 'Poppins-SemiBold' : 'PoppinsSemiBold-B2Bw',
    },
    menuContainer: {
      backgroundColor: theme.colors.cardBackground,
      borderRadius: 16,
      paddingVertical: 6,
      shadowColor: theme.colors.shadow || '#000',
      shadowOffset: { width: 0, height: 6 },
      shadowOpacity: 0.15,
      shadowRadius: 24,
      elevation: 8,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    text: {
      color: theme.colors.text,
      fontSize: 15,
      fontWeight: '500',
      textAlign: 'center',
    },
    menuOption: {
      paddingVertical: 14,
      paddingHorizontal: 20,
      flexDirection: 'row',
      alignItems: 'center',
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
      backgroundColor: theme.colors.cardBackground,
      margin: 2,
    },
    lastMenuOption: {
      borderBottomWidth: 0,
    },
    menuText: {
      fontSize: 16,
      color: theme.colors.icon,
      fontFamily:
        Platform.OS === 'ios' ? 'Poppins-Regular' : 'PoppinsRegular-B2Bw',
    },
    tooltipContent: {
      color: theme.colors.text,
    },
  });

  return (
    <Menu ref={menuRef} onClose={hideMenu}>
      <MenuTrigger>
        <Tooltip
          isVisible={toolTipStage === Stage.Menu}
          content={
            <Text style={styles.tooltipContent}>Click to View Menu</Text>
          }
          placement="top"
          displayInsets={{ top: 0, bottom: 0, left: 0, right: 0 }}
          accessible={false}
          useInteractionManager={true}
          onClose={() => {
            menuRef.current.open();
            setToolTipStage(Stage.History);
          }}>
          <TouchableOpacity
            onPress={openMenu}
            activeOpacity={0.6}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            delayPressIn={0}>
            {props.menuAnchor(visible)}
          </TouchableOpacity>
        </Tooltip>
      </MenuTrigger>
      <MenuOptions optionsContainerStyle={styles.menuContainer}>
        <ScrollView style={{ maxHeight: 600 }}>
          {props.menuItems.map((element, index) => (
            <MenuOption
              key={index}
              style={[
                styles.menuOption,
                index === props.menuItems.length - 1 && styles.lastMenuOption,
              ]}
              onSelect={() => menuAction(element[1])}>
              <Text style={styles.menuText}>{element[0]}</Text>
            </MenuOption>
          ))}
        </ScrollView>
      </MenuOptions>
    </Menu>
  );
}

